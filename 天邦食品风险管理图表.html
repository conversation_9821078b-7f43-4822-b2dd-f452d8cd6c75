<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>天邦食品风险管理研究 - 数据可视化</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .chart-container {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .chart-title {
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
        }
        canvas {
            max-height: 400px;
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
        }
        .chart-description {
            margin-top: 15px;
            padding: 10px;
            background-color: #e8f4f8;
            border-left: 4px solid #3498db;
            font-size: 14px;
            color: #555;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>天邦食品风险管理研究 - 数据可视化分析</h1>

        <!-- 图表1：营业收入与净利润变化趋势 -->
        <div class="chart-container">
            <div class="chart-title">图1：天邦食品营业收入与净利润变化趋势（2018-2024年）</div>
            <canvas id="revenueChart"></canvas>
            <div class="chart-description">
                <strong>分析：</strong>天邦食品在2019-2020年猪价高峰期实现盈利，但2021年后连续亏损，显示出对市场波动的脆弱性。
            </div>
        </div>

        <!-- 图表2：区域产能分布 -->
        <div class="chart-container">
            <div class="chart-title">图2：天邦食品区域产能分布（2024年）</div>
            <canvas id="regionChart"></canvas>
            <div class="chart-description">
                <strong>分析：</strong>华东地区是主要产能基地，占总产能的38.9%，但各区域产能利用率均偏低。
            </div>
        </div>

        <!-- 图表3：流动性指标对比 -->
        <div class="chart-container">
            <div class="chart-title">图3：天邦食品与行业平均流动性指标对比（2024年）</div>
            <canvas id="liquidityChart"></canvas>
            <div class="chart-description">
                <strong>分析：</strong>天邦食品各项流动性指标均远低于行业平均水平，流动性风险极高。
            </div>
        </div>

        <!-- 图表4：债务结构及逾期情况 -->
        <div class="chart-container">
            <div class="chart-title">图4：天邦食品债务结构及逾期情况（2024年末）</div>
            <canvas id="debtChart"></canvas>
            <div class="chart-description">
                <strong>分析：</strong>银行借款占比最高，但公司债券逾期率最严重，达到60.5%。
            </div>
        </div>

        <!-- 图表5：生猪价格与单头毛利变化 -->
        <div class="chart-container">
            <div class="chart-title">图5：生猪价格与天邦食品单头毛利变化（2018-2024年）</div>
            <canvas id="priceChart"></canvas>
            <div class="chart-description">
                <strong>分析：</strong>生猪价格波动直接影响公司盈利能力，2023-2024年价格低迷导致单头亏损。
            </div>
        </div>

        <!-- 图表6：疫病防控投入与死亡率对比 -->
        <div class="chart-container">
            <div class="chart-title">图6：天邦食品疫病防控投入与死亡率对比（2018-2024年）</div>
            <canvas id="diseaseChart"></canvas>
            <div class="chart-description">
                <strong>分析：</strong>尽管防控投入逐年增加，但死亡率仍高于行业平均水平2.3个百分点。
            </div>
        </div>

        <!-- 图表7：风险管理能力评估雷达图 -->
        <div class="chart-container">
            <div class="chart-title">图7：天邦食品风险管理能力评估</div>
            <canvas id="radarChart"></canvas>
            <div class="chart-description">
                <strong>分析：</strong>天邦食品在各项风险管理能力上均低于行业平均水平，财务风险管理能力最弱。
            </div>
        </div>

        <!-- 图表8：非洲猪瘟疫情损失构成 -->
        <div class="chart-container">
            <div class="chart-title">图8：2018-2019年非洲猪瘟疫情损失构成</div>
            <canvas id="lossChart"></canvas>
            <div class="chart-description">
                <strong>分析：</strong>直接扑杀损失占总损失的74%，反映出疫病防控体系的不完善。
            </div>
        </div>

        <!-- 图表9：债务重组前后对比 -->
        <div class="chart-container">
            <div class="chart-title">图9：天邦食品债务重组方案前后对比</div>
            <canvas id="restructureChart"></canvas>
            <div class="chart-description">
                <strong>分析：</strong>通过债务重组，总负债可减少38.6%，有助于缓解财务压力。
            </div>
        </div>

        <!-- 图表10：套期保值覆盖率提升计划 -->
        <div class="chart-container">
            <div class="chart-title">图10：套期保值覆盖率逐月提升计划</div>
            <canvas id="hedgeChart"></canvas>
            <div class="chart-description">
                <strong>分析：</strong>计划将套期保值比例从5%逐步提升至50%，有效对冲价格波动风险。
            </div>
        </div>
    </div>

    <script>
        // 图表1：营业收入与净利润变化趋势
        const ctx1 = document.getElementById('revenueChart').getContext('2d');
        new Chart(ctx1, {
            type: 'line',
            data: {
                labels: ['2018', '2019', '2020', '2021', '2022', '2023', '2024'],
                datasets: [{
                    label: '营业收入(亿元)',
                    data: [45.2, 89.6, 125.8, 98.4, 72.3, 68.9, 73.2],
                    borderColor: '#3498db',
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    yAxisID: 'y'
                }, {
                    label: '净利润(亿元)',
                    data: [2.8, 15.3, 28.9, -12.6, -23.4, -18.7, -15.2],
                    borderColor: '#e74c3c',
                    backgroundColor: 'rgba(231, 76, 60, 0.1)',
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: { display: true, text: '营业收入(亿元)' }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: { display: true, text: '净利润(亿元)' },
                        grid: { drawOnChartArea: false }
                    }
                }
            }
        });

        // 图表2：区域产能分布
        const ctx2 = document.getElementById('regionChart').getContext('2d');
        new Chart(ctx2, {
            type: 'pie',
            data: {
                labels: ['华东', '华中', '华南', '西南'],
                datasets: [{
                    data: [420, 280, 200, 180],
                    backgroundColor: ['#3498db', '#2ecc71', '#f39c12', '#9b59b6']
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: { position: 'bottom' },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((context.parsed / total) * 100).toFixed(1);
                                return context.label + ': ' + context.parsed + '万头 (' + percentage + '%)';
                            }
                        }
                    }
                }
            }
        });

        // 图表3：流动性指标对比
        const ctx3 = document.getElementById('liquidityChart').getContext('2d');
        new Chart(ctx3, {
            type: 'bar',
            data: {
                labels: ['流动比率', '速动比率', '现金比率'],
                datasets: [{
                    label: '天邦食品',
                    data: [0.42, 0.31, 0.08],
                    backgroundColor: '#e74c3c'
                }, {
                    label: '行业平均',
                    data: [1.20, 0.80, 0.25],
                    backgroundColor: '#2ecc71'
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: { beginAtZero: true, max: 1.5 }
                }
            }
        });

        // 图表4：债务结构及逾期情况
        const ctx4 = document.getElementById('debtChart').getContext('2d');
        new Chart(ctx4, {
            type: 'bar',
            data: {
                labels: ['银行借款', '公司债券', '其他借款', '应付账款'],
                datasets: [{
                    label: '总额(亿元)',
                    data: [78.5, 25.8, 15.7, 38.2],
                    backgroundColor: '#3498db'
                }, {
                    label: '已逾期(亿元)',
                    data: [32.5, 15.6, 12.3, 8.9],
                    backgroundColor: '#e74c3c'
                }]
            },
            options: {
                responsive: true,
                scales: {
                    x: { stacked: false },
                    y: { stacked: false, beginAtZero: true }
                }
            }
        });

        // 图表5：生猪价格与单头毛利变化
        const ctx5 = document.getElementById('priceChart').getContext('2d');
        new Chart(ctx5, {
            type: 'line',
            data: {
                labels: ['2018', '2019', '2020', '2021', '2022', '2023', '2024'],
                datasets: [{
                    label: '生猪均价(元/kg)',
                    data: [14.2, 34.6, 30.1, 16.8, 21.4, 15.3, 16.8],
                    borderColor: '#f39c12',
                    backgroundColor: 'rgba(243, 156, 18, 0.1)',
                    yAxisID: 'y'
                }, {
                    label: '单头毛利(元)',
                    data: [156, 1452, 1108, 177, 368, -125, -46],
                    borderColor: '#9b59b6',
                    backgroundColor: 'rgba(155, 89, 182, 0.1)',
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: { display: true, text: '生猪均价(元/kg)' }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: { display: true, text: '单头毛利(元)' },
                        grid: { drawOnChartArea: false }
                    }
                }
            }
        });

        // 图表6：疫病防控投入与死亡率对比
        const ctx6 = document.getElementById('diseaseChart').getContext('2d');
        new Chart(ctx6, {
            type: 'bar',
            data: {
                labels: ['2018', '2019', '2020', '2021', '2022', '2023', '2024'],
                datasets: [{
                    label: '防控投入(亿元)',
                    data: [2.8, 5.2, 4.8, 4.5, 4.3, 4.1, 4.2],
                    backgroundColor: '#3498db',
                    yAxisID: 'y'
                }, {
                    label: '天邦死亡率(%)',
                    data: [12.3, 15.6, 9.8, 8.9, 8.7, 8.6, 8.5],
                    type: 'line',
                    borderColor: '#e74c3c',
                    backgroundColor: 'rgba(231, 76, 60, 0.1)',
                    yAxisID: 'y1'
                }, {
                    label: '行业平均死亡率(%)',
                    data: [8.5, 11.2, 7.8, 6.8, 6.5, 6.3, 6.2],
                    type: 'line',
                    borderColor: '#2ecc71',
                    backgroundColor: 'rgba(46, 204, 113, 0.1)',
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: { display: true, text: '防控投入(亿元)' }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: { display: true, text: '死亡率(%)' },
                        grid: { drawOnChartArea: false }
                    }
                }
            }
        });

        // 图表7：风险管理能力评估雷达图
        const ctx7 = document.getElementById('radarChart').getContext('2d');
        new Chart(ctx7, {
            type: 'radar',
            data: {
                labels: ['市场价格风险', '疫病风险', '政策风险', '财务风险', '环保风险'],
                datasets: [{
                    label: '天邦食品',
                    data: [53.3, 45.0, 43.3, 36.7, 55.0],
                    borderColor: '#e74c3c',
                    backgroundColor: 'rgba(231, 76, 60, 0.2)'
                }, {
                    label: '行业平均',
                    data: [72.0, 68.5, 65.2, 75.8, 70.3],
                    borderColor: '#2ecc71',
                    backgroundColor: 'rgba(46, 204, 113, 0.2)'
                }]
            },
            options: {
                responsive: true,
                scales: {
                    r: {
                        beginAtZero: true,
                        max: 100
                    }
                }
            }
        });

        // 图表8：非洲猪瘟疫情损失构成
        const ctx8 = document.getElementById('lossChart').getContext('2d');
        new Chart(ctx8, {
            type: 'pie',
            data: {
                labels: ['直接扑杀损失', '防控投入', '产能损失', '其他损失'],
                datasets: [{
                    data: [18.5, 3.2, 2.8, 0.5],
                    backgroundColor: ['#e74c3c', '#f39c12', '#3498db', '#95a5a6']
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: { position: 'bottom' },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((context.parsed / total) * 100).toFixed(1);
                                return context.label + ': ' + context.parsed + '亿元 (' + percentage + '%)';
                            }
                        }
                    }
                }
            }
        });

        // 图表9：债务重组前后对比
        const ctx9 = document.getElementById('restructureChart').getContext('2d');
        new Chart(ctx9, {
            type: 'bar',
            data: {
                labels: ['银行借款', '公司债券', '其他借款', '应付账款'],
                datasets: [{
                    label: '重组前(亿元)',
                    data: [78.5, 25.8, 15.7, 38.2],
                    backgroundColor: '#e74c3c'
                }, {
                    label: '重组后(亿元)',
                    data: [39.3, 10.3, 9.4, 38.2],
                    backgroundColor: '#2ecc71'
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: { beginAtZero: true }
                }
            }
        });

        // 图表10：套期保值覆盖率提升计划
        const ctx10 = document.getElementById('hedgeChart').getContext('2d');
        new Chart(ctx10, {
            type: 'line',
            data: {
                labels: ['2025年1月', '2025年2月', '2025年3月', '2025年4月', '后续月份'],
                datasets: [{
                    label: '套保比例(%)',
                    data: [30, 35, 40, 45, 50],
                    borderColor: '#3498db',
                    backgroundColor: 'rgba(52, 152, 219, 0.3)',
                    fill: true
                }, {
                    label: '预期锁定收入(亿元)',
                    data: [3.4, 4.2, 5.1, 5.7, 6.4],
                    borderColor: '#2ecc71',
                    backgroundColor: 'rgba(46, 204, 113, 0.3)',
                    fill: true,
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: { display: true, text: '套保比例(%)' }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: { display: true, text: '预期锁定收入(亿元)' },
                        grid: { drawOnChartArea: false }
                    }
                }
            }
        });
    </script>
</body>
</html>
